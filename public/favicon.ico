<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 Page Not Found</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2290D5',
                        secondary: '#FF8C00',
                    },
                    fontFamily: {
                        sans: ['Roboto', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        html, body {
            height: 100%;
            overflow: hidden;
        }
        .container-404 {
            height: 100vh;
            max-height: -webkit-fill-available;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="container-404 mx-auto px-4 flex flex-col md:flex-row items-center justify-between">
        <!-- Left Column - Image -->
<div class="hidden md:flex h-full w-auto md:w-1/2 justify-center p-4">
    <img src="./assets/images/electric2.png" alt="404 Illustration" class="max-w-md w-full h-auto object-contain">
</div>

<!-- Right Column - Content -->
<div class="w-full md:w-1/2 flex items-center justify-center text-center p-4 h-full">
    <div class="max-w-md">
        <!-- 404 Text -->
        <h1 class="text-7xl md:text-8xl font-bold text-primary mb-2">404</h1>
        <h2 class="text-2xl md:text-3xl font-medium text-gray-800 mb-6">Page Not Found</h2>
        
        <p class="text-[16px] text-[#7F7F7F] mb-4">
            Oops! It seems the page you were looking for doesn’t exist or may have been moved. Please check the URL or return to the homepage to continue exploring.
        </p>

        <a href="https://skillrank.io/" class="inline-block px-6 py-2 md:px-8 md:py-3 bg-primary hover:bg-opacity-90 text-white font-medium rounded-full shadow-lg transition-all duration-200 transform hover:-translate-y-1">
            GO HOME
            <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
        </a>

        <!-- Decorative Elements -->
        <div class="fixed bottom-10 right-10 w-24 h-24 md:w-32 md:h-32 rounded-full bg-secondary opacity-10 blur-xl -z-10"></div>
        <div class="fixed top-20 left-20 w-20 h-20 md:w-24 md:h-24 rounded-full bg-primary opacity-10 blur-xl -z-10"></div>
    </div>
</div>

    </div>
</body>
</html>