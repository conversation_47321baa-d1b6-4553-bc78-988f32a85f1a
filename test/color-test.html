<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Color System Test</title>
    <link rel="stylesheet" href="../src/style.css">
    <style>
        body {
            padding: 2rem;
            font-family: system-ui, sans-serif;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid var(--border-color);
            border-radius: 12px;
        }
        
        .color-swatch {
            display: inline-block;
            width: 100px;
            height: 60px;
            margin: 0.5rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            position: relative;
        }
        
        .color-label {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 0.75rem;
            color: var(--text-muted);
        }
        
        .contrast-test {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .contrast-item {
            padding: 1rem;
            border-radius: 8px;
            min-width: 200px;
            text-align: center;
            font-weight: 500;
        }
        
        .component-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <h1 class="text-primary">Professional Color System Test</h1>
    
    <!-- Core Colors Section -->
    <div class="test-section">
        <h2 class="text-primary">Core Theme Colors</h2>
        <div>
            <div class="color-swatch bg-primary">
                <div class="color-label">Primary</div>
            </div>
            <div class="color-swatch bg-secondary">
                <div class="color-label">Secondary</div>
            </div>
            <div class="color-swatch bg-light">
                <div class="color-label">Light</div>
            </div>
            <div class="color-swatch bg-muted">
                <div class="color-label">Muted</div>
            </div>
        </div>
    </div>
    
    <!-- Status Colors Section -->
    <div class="test-section">
        <h2 class="text-primary">Status Colors</h2>
        <div>
            <div class="color-swatch bg-success">
                <div class="color-label">Success</div>
            </div>
            <div class="color-swatch bg-warning">
                <div class="color-label">Warning</div>
            </div>
            <div class="color-swatch bg-info">
                <div class="color-label">Info</div>
            </div>
            <div class="color-swatch bg-error">
                <div class="color-label">Error</div>
            </div>
        </div>
    </div>
    
    <!-- Contrast Testing -->
    <div class="test-section">
        <h2 class="text-primary">Accessibility Contrast Tests</h2>
        <div class="contrast-test">
            <div class="contrast-item bg-primary text-primary">
                Primary text on white background
            </div>
            <div class="contrast-item bg-secondary text-secondary">
                White text on navy background
            </div>
            <div class="contrast-item bg-success-light text-success">
                Success text on light background
            </div>
            <div class="contrast-item bg-warning-light text-warning">
                Warning text on light background
            </div>
            <div class="contrast-item bg-info-light text-info">
                Info text on light background
            </div>
            <div class="contrast-item bg-error-light text-error">
                Error text on light background
            </div>
        </div>
    </div>
    
    <!-- Component Showcase -->
    <div class="test-section">
        <h2 class="text-primary">Professional Components</h2>
        <div class="component-showcase">
            
            <!-- Status Badges -->
            <div class="card">
                <h3 class="text-primary">Status Badges</h3>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-top: 1rem;">
                    <span class="status-badge success">✓ Completed</span>
                    <span class="status-badge warning">⚠ In Progress</span>
                    <span class="status-badge info">ℹ Processing</span>
                    <span class="status-badge error">✗ Failed</span>
                </div>
            </div>
            
            <!-- Progress Indicators -->
            <div class="card">
                <h3 class="text-primary">Progress Indicators</h3>
                <div style="margin-top: 1rem;">
                    <div class="progress-container">
                        <div class="progress-bar-professional" style="width: 75%;"></div>
                    </div>
                    <p class="text-muted" style="margin-top: 0.5rem; font-size: 0.9rem;">75% Complete</p>
                </div>
            </div>
            
            <!-- Alert Components -->
            <div class="card">
                <h3 class="text-primary">Alert Messages</h3>
                <div style="margin-top: 1rem;">
                    <div class="alert success">
                        <span>✓</span>
                        <span>Operation completed successfully!</span>
                    </div>
                    <div class="alert warning">
                        <span>⚠</span>
                        <span>Please review your settings.</span>
                    </div>
                    <div class="alert info">
                        <span>ℹ</span>
                        <span>Processing your request...</span>
                    </div>
                    <div class="alert error">
                        <span>✗</span>
                        <span>An error occurred. Please try again.</span>
                    </div>
                </div>
            </div>
            
            <!-- Document Processing (Theme-Consistent) -->
            <div class="card">
                <h3 class="text-primary">Document Processing</h3>
                <div style="margin-top: 1rem;">
                    <div class="ocr-processing">
                        <div class="ocr-header">
                            <span>📄</span>
                            <span>Document Processing</span>
                        </div>
                        <div class="ocr-steps">
                            <div class="ocr-step completed">
                                <div class="step-icon completed"></div>
                                <span>Scanning document structure...</span>
                            </div>
                            <div class="ocr-step completed">
                                <div class="step-icon completed"></div>
                                <span>Extracting text and data fields...</span>
                            </div>
                            <div class="ocr-step active">
                                <div class="step-icon active"></div>
                                <span>Validating extracted information...</span>
                            </div>
                            <div class="ocr-step">
                                <div class="step-icon pending"></div>
                                <span>Finalizing results...</span>
                            </div>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: 75%;"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Database Operation (Theme-Consistent) -->
            <div class="card">
                <h3 class="text-primary">Database Operations</h3>
                <div style="margin-top: 1rem;">
                    <div class="database-operation">
                        <span>🗄️</span>
                        <span>Updating employee record...</span>
                    </div>
                </div>
            </div>
            
            <!-- Form Elements -->
            <div class="card">
                <h3 class="text-primary">Form Elements</h3>
                <div style="margin-top: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Professional Input</label>
                        <input type="text" class="form-input" placeholder="Enter text here..." />
                    </div>
                    <button style="margin-top: 0.5rem;">Professional Button</button>
                </div>
            </div>
            
            <!-- Interactive Cards -->
            <div class="card hover-overlay" style="cursor: pointer;">
                <h3 class="text-primary">Interactive Card</h3>
                <p class="text-muted">This card has hover effects and professional styling. Try hovering over it!</p>
            </div>
            
            <!-- Shadow Examples -->
            <div style="display: flex; flex-direction: column; gap: 1rem;">
                <div class="card shadow-light">
                    <h4 class="text-primary">Light Shadow</h4>
                    <p class="text-muted">Subtle elevation</p>
                </div>
                <div class="card shadow-medium">
                    <h4 class="text-primary">Medium Shadow</h4>
                    <p class="text-muted">Standard elevation</p>
                </div>
                <div class="card shadow-strong">
                    <h4 class="text-primary">Strong Shadow</h4>
                    <p class="text-muted">Prominent elevation</p>
                </div>
            </div>
            
        </div>
    </div>
    
    <!-- Usage Guidelines -->
    <div class="test-section">
        <h2 class="text-primary">Usage Guidelines</h2>
        <div class="alert info">
            <span>ℹ</span>
            <div>
                <strong>Professional Color System Active</strong><br>
                All components now use consistent CSS custom properties for maintainable, accessible, and professional styling.
                <ul style="margin-top: 0.5rem; margin-bottom: 0;">
                    <li>All colors meet WCAG 2.1 AA accessibility standards</li>
                    <li>Consistent hover and focus states across components</li>
                    <li>Semantic color naming for better maintainability</li>
                    <li>Professional shadow and spacing system</li>
                </ul>
            </div>
        </div>
    </div>
    
</body>
</html>