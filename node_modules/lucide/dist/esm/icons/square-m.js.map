{"version": 3, "file": "square-m.js", "sources": ["../../../src/icons/square-m.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name square-m\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik04IDE2VjhsNCA0IDQtNHY4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-m\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst SquareM: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2' }],\n  ['path', { d: 'M8 16V8l4 4 4-4v8' }],\n];\n\nexport default SquareM;\n"], "names": [], "mappings": ";;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAoB,CAAA,CAAA,CAAA;AAAA,CACxB,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAI,KAAK,CAAA;AAAA,CAAA,CAC/D,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE,CAAA,CAAA,CAAG,oBAAqB,CAAA;AACrC,CAAA;;"}