{"version": 3, "file": "ungroup.js", "sources": ["../../../src/icons/ungroup.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name ungroup\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI2IiB4PSI1IiB5PSI0IiByeD0iMSIgLz4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSI2IiB4PSIxMSIgeT0iMTQiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ungroup\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst Ungroup: IconNode = [\n  ['rect', { width: '8', height: '6', x: '5', y: '4', rx: '1' }],\n  ['rect', { width: '8', height: '6', x: '11', y: '14', rx: '1' }],\n];\n\nexport default Ungroup;\n"], "names": [], "mappings": ";;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAoB,CAAA,CAAA,CAAA;AAAA,CACxB,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAI,KAAK,CAAA;AAAA,CAC7D,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,KAAA,CAAO,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAG,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,IAAM,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,EAAK,CAAA;AACjE,CAAA;;"}