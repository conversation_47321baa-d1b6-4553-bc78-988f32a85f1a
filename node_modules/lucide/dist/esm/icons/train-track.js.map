{"version": 3, "file": "train-track.js", "sources": ["../../../src/icons/train-track.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name train-track\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxNyAxNyAyIiAvPgogIDxwYXRoIGQ9Im0yIDE0IDggOCIgLz4KICA8cGF0aCBkPSJtNSAxMSA4IDgiIC8+CiAgPHBhdGggZD0ibTggOCA4IDgiIC8+CiAgPHBhdGggZD0ibTExIDUgOCA4IiAvPgogIDxwYXRoIGQ9Im0xNCAyIDggOCIgLz4KICA8cGF0aCBkPSJNNyAyMiAyMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/train-track\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst TrainTrack: IconNode = [\n  ['path', { d: 'M2 17 17 2' }],\n  ['path', { d: 'm2 14 8 8' }],\n  ['path', { d: 'm5 11 8 8' }],\n  ['path', { d: 'm8 8 8 8' }],\n  ['path', { d: 'm11 5 8 8' }],\n  ['path', { d: 'm14 2 8 8' }],\n  ['path', { d: 'M7 22 22 7' }],\n];\n\nexport default TrainTrack;\n"], "names": [], "mappings": ";;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;AAAA,CAC3B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAc,CAAA;AAAA,CAC5B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAa,CAAA;AAAA,CAC3B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAa,CAAA;AAAA,CAC3B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAY,CAAA;AAAA,CAC1B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAa,CAAA;AAAA,CAC3B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAa,CAAA;AAAA,CAAA,CAC3B,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE,CAAA,CAAA,CAAG,aAAc,CAAA;AAC9B,CAAA;;"}