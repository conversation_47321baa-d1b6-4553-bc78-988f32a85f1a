{"version": 3, "file": "move-down-left.js", "sources": ["../../../src/icons/move-down-left.ts"], "sourcesContent": ["import defaultAttributes from '../defaultAttributes';\nimport type { IconNode } from '../types';\n\n/**\n * @name move-down-left\n * @description Lucide SVG icon node.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTlINVYxMyIgLz4KICA8cGF0aCBkPSJNMTkgNUw1IDE5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/move-down-left\n * @see https://lucide.dev/guide/packages/lucide - Documentation\n *\n * @returns {Array}\n *\n */\nconst MoveDownLeft: IconNode = [\n  ['path', { d: 'M11 19H5V13' }],\n  ['path', { d: 'M19 5L5 19' }],\n];\n\nexport default MoveDownLeft;\n"], "names": [], "mappings": ";;;;;;;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,YAAyB,CAAA,CAAA,CAAA;AAAA,CAC7B,CAAA,CAAC,MAAA,CAAQ,CAAA,CAAA,CAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAe,CAAA;AAAA,CAAA,CAC7B,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE,CAAA,CAAA,CAAG,aAAc,CAAA;AAC9B,CAAA;;"}