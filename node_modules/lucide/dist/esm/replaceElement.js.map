{"version": 3, "file": "replaceElement.js", "sources": ["../../src/replaceElement.ts"], "sourcesContent": ["import createElement from './createElement';\nimport defaultAttributes from './defaultAttributes';\nimport { Icons } from './types';\n\nexport type CustomAttrs = { [attr: string]: any };\n\n/**\n * Get the attributes of an HTML element.\n * @param {HTMLElement} element\n * @returns {Object}\n */\nexport const getAttrs = (element: Element): Record<string, string> =>\n  Array.from(element.attributes).reduce<Record<string, string>>((attrs, attr) => {\n    attrs[attr.name] = attr.value;\n    return attrs;\n  }, {});\n\n/**\n * Gets the classNames of an attributes Object.\n * @param {Object} attrs\n * @returns {Array}\n */\nexport const getClassNames = (\n  attrs: Record<string, string | string[]> | string,\n): string | string[] => {\n  if (typeof attrs === 'string') return attrs;\n  if (!attrs || !attrs.class) return '';\n  if (attrs.class && typeof attrs.class === 'string') {\n    return attrs.class.split(' ');\n  }\n  if (attrs.class && Array.isArray(attrs.class)) {\n    return attrs.class;\n  }\n  return '';\n};\n\n/**\n * Combines the classNames of array of classNames to a String\n * @param {array} arrayOfClassnames\n * @returns {string}\n */\nexport const combineClassNames = (\n  arrayOfClassnames: (string | Record<string, string | string[]>)[],\n) => {\n  const classNameArray = arrayOfClassnames.flatMap(getClassNames);\n\n  return classNameArray\n    .map((classItem) => classItem.trim())\n    .filter(Boolean)\n    .filter((value, index, self) => self.indexOf(value) === index)\n    .join(' ');\n};\n\nconst toPascalCase = (string: string): string =>\n  string.replace(/(\\w)(\\w*)(_|-|\\s*)/g, (g0, g1, g2) => g1.toUpperCase() + g2.toLowerCase());\n\ninterface ReplaceElementOptions {\n  nameAttr: string;\n  icons: Icons;\n  attrs: Record<string, string>;\n}\n\n/**\n * ReplaceElement, replaces the given element with the created icon.\n * @param {HTMLElement} element\n * @param {{ nameAttr: string, icons: object, attrs: object }} options: { nameAttr, icons, attrs }\n * @returns {Function}\n */\nconst replaceElement = (element: Element, { nameAttr, icons, attrs }: ReplaceElementOptions) => {\n  const iconName = element.getAttribute(nameAttr);\n\n  if (iconName == null) return;\n\n  const ComponentName = toPascalCase(iconName);\n\n  const iconNode = icons[ComponentName];\n\n  if (!iconNode) {\n    return console.warn(\n      `${element.outerHTML} icon name was not found in the provided icons object.`,\n    );\n  }\n\n  const elementAttrs = getAttrs(element);\n\n  const iconAttrs = {\n    ...defaultAttributes,\n    'data-lucide': iconName,\n    ...attrs,\n    ...elementAttrs,\n  };\n\n  const classNames = combineClassNames(['lucide', `lucide-${iconName}`, elementAttrs, attrs]);\n\n  if (classNames) {\n    Object.assign(iconAttrs, {\n      class: classNames,\n    });\n  }\n\n  const svgElement = createElement(iconNode, iconAttrs);\n\n  return element.parentNode?.replaceChild(svgElement, element);\n};\n\nexport default replaceElement;\n"], "names": [], "mappings": ";;;;;;;;;;AAWa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAW,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KACvB,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,IAAS,CAAA,CAAA,CAAA,CAAA,CAAA;AAC7E,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,IAAI,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACxB,CAAA,CAAO,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA,EAAG,CAAE,CAAA,CAAA;AAOM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAC3B,CAAA,CAAA,CAAA,CAAA,CACsB,CAAA,CAAA,CAAA,CAAA,CAAA;AACtB,CAAA,CAAI,IAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAC,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA;AAClD,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,GAAG,CAAA;AAAA,CAAA,CAAA;AAE9B,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA;AAC7C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAEf,CAAA,CAAO,OAAA,CAAA,CAAA;AACT,CAAA;AAOa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAC/B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,CAAA,CAAA,CAAA,CAAA,CAAA;AACH,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,aAAa,CAAA;AAE9D,CAAA,CAAO,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACJ,IAAI,CAAC,SAAA,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAK,EAAC,CACnC,CAAA,MAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CACd,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAC,KAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,IAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAC5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,GAAG,CAAA;AACb,CAAA;AAEA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAA,CAAA,CAAA,CAAC,CACpB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,QAAQ,CAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,EAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,aAAa,CAAA;AAc3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,iBAAiB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA,CAAA,CAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAmC,CAAA,CAAA,CAAA,CAAA,CAAA;AAC9F,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,QAAQ,CAAA;AAE9C,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEtB,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,aAAa,QAAQ,CAAA;AAE3C,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,MAAM,aAAa,CAAA;AAEpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACb,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACtB,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAGF,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,SAAS,OAAO,CAAA;AAErC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAY,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAChB,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACH,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACf,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACH,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACL,CAAA,CAAA;AAEA,CAAA,CAAM,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,kBAAkB,CAAC,QAAA,CAAU,CAAA,SAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAI,YAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;AAE1F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,UAAY,CAAA,CAAA;AACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAO,SAAW,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACvB,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA;AAAA,CAAA,CAAA;AAGH,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;AAEpD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA;AAC7D,CAAA;;"}