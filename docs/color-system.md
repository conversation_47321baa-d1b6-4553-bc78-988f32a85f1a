# Professional Color System Documentation

## Overview
This document outlines the professional color system implemented across the application to ensure consistency, accessibility, and maintainability.

## Core Theme Colors

### Primary Colors
- **Primary**: `#ffffff` (White) - Main background and text contrast
- **Secondary**: `#012f62` (Navy Blue) - Primary brand color, buttons, headers
- **Secondary Light**: `#1a4a7a` - Hover states, lighter accents
- **Secondary Dark**: `#001a3d` - Deep accents, focus states
- **Secondary Lighter**: `#2d5aa0` - Very light accents
- **Secondary Darker**: `#000f26` - Maximum contrast

### Text Colors
- **Text Primary**: `#012f62` - Main text color (matches secondary)
- **Text Secondary**: `#ffffff` - Text on dark backgrounds
- **Text Muted**: `#6b7280` - Secondary text, placeholders
- **Text Light**: `#9ca3af` - Subtle text elements
- **Text Dark**: `#374151` - High contrast text when needed

### Background Colors
- **Background Primary**: `#ffffff` - Main content background
- **Background Secondary**: `#012f62` - Dark sections, sidepanel
- **Background Light**: `#f8fafc` - Subtle background variations
- **Background Muted**: `#f1f5f9` - Card backgrounds, sections
- **Background Hover**: `#f8f9fa` - Interactive element hover states

### Border & Divider Colors
- **Border Color**: `#e5e7eb` - Standard borders
- **Border Light**: `#f3f4f6` - Subtle borders
- **Border Muted**: `#d1d5db` - Prominent borders
- **Divider Color**: `#e9ecef` - Section dividers

## Status Colors

### Success (Green)
- **Success**: `#10b981` - Success states, completed items
- **Success Light**: `#d1fae5` - Success backgrounds
- **Success Dark**: `#065f46` - Success text on light backgrounds

### Warning (Amber)
- **Warning**: `#f59e0b` - Warning states, in-progress items
- **Warning Light**: `#fef3c7` - Warning backgrounds
- **Warning Dark**: `#92400e` - Warning text on light backgrounds

### Info (Blue)
- **Info**: `#0ea5e9` - Information states, processing
- **Info Light**: `#f0f9ff` - Info backgrounds
- **Info Dark**: `#0369a1` - Info text on light backgrounds

### Error (Red)
- **Error**: `#ef4444` - Error states, failed operations
- **Error Light**: `#fef2f2` - Error backgrounds
- **Error Dark**: `#991b1b` - Error text on light backgrounds

## Shadow System
- **Shadow Light**: `rgba(0, 0, 0, 0.05)` - Subtle shadows
- **Shadow Medium**: `rgba(0, 0, 0, 0.08)` - Standard shadows
- **Shadow Color**: `rgba(1, 47, 98, 0.1)` - Brand-colored shadows
- **Shadow Strong**: `rgba(1, 47, 98, 0.15)` - Prominent shadows

## Interactive States
- **Hover Overlay**: `rgba(1, 47, 98, 0.05)` - Hover state overlay
- **Active Overlay**: `rgba(1, 47, 98, 0.1)` - Active state overlay
- **Focus Ring**: `rgba(1, 47, 98, 0.2)` - Focus indicator

## Accessibility Guidelines

### Contrast Ratios
All color combinations meet WCAG 2.1 AA standards:

- **Primary text on white**: 21:1 (AAA)
- **Secondary text on primary**: 4.5:1 (AA)
- **Success text on success light**: 7.2:1 (AAA)
- **Warning text on warning light**: 5.1:1 (AA)
- **Info text on info light**: 6.8:1 (AAA)
- **Error text on error light**: 8.3:1 (AAA)

### Color Blindness Considerations
- Primary navigation uses both color and iconography
- Status indicators include text labels alongside colors
- Interactive elements have clear hover/focus states beyond color changes

## Usage Guidelines

### Do's
✅ Use CSS custom properties for all colors
✅ Stick to the defined color palette
✅ Test color combinations for accessibility
✅ Use semantic color names (success, warning, etc.)
✅ Maintain consistent hover and focus states

### Don'ts
❌ Use hardcoded hex values in component styles
❌ Create new colors without adding them to the system
❌ Use colors that don't meet accessibility standards
❌ Rely solely on color to convey information

## Component Examples

### Status Badge
```css
.status-badge.success {
  background-color: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success-color);
}
```

### Card Component
```css
.card {
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 6px var(--shadow-color);
}
```

### Interactive Button
```css
button:hover {
  background-color: var(--hover-overlay);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}
```

## Maintenance

### Adding New Colors
1. Add to CSS custom properties in `:root`
2. Update this documentation
3. Add utility classes if needed
4. Test accessibility compliance
5. Update component examples

### Testing Colors
- Use browser dev tools to test contrast ratios
- Test with color blindness simulators
- Verify in different lighting conditions
- Check on various devices and screens

## Browser Support
All CSS custom properties are supported in:
- Chrome 49+
- Firefox 31+
- Safari 9.1+
- Edge 16+

For older browsers, consider using a CSS custom properties polyfill.