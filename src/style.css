:root {
  /* Core Theme Colors */
  --primary-color: #ffffff;
  --secondary-color: #012f62;
  --secondary-light: #1a4a7a;
  --secondary-dark: #001a3d;
  --secondary-lighter: #2d5aa0;
  --secondary-darker: #000f26;
  
  /* Text Colors */
  --text-primary: #012f62;
  --text-secondary: #ffffff;
  --text-muted: #6b7280;
  --text-light: #9ca3af;
  --text-dark: #374151;
  
  /* Background Colors */
  --background-primary: #ffffff;
  --background-secondary: #012f62;
  --background-light: #f8fafc;
  --background-muted: #f1f5f9;
  --background-hover: #f8f9fa;
  
  /* Border & Divider Colors */
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-muted: #d1d5db;
  --divider-color: #e9ecef;
  
  /* Shadow Colors */
  --shadow-color: rgba(1, 47, 98, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-medium: rgba(0, 0, 0, 0.08);
  --shadow-strong: rgba(1, 47, 98, 0.15);
  
  /* Status Colors */
  --success-color: #10b981;
  --success-light: #d1fae5;
  --success-dark: #065f46;
  --warning-color: #d97706;
  --warning-light: #fef3c7;
  --warning-dark: #92400e;
  --info-color: #0ea5e9;
  --info-light: #f0f9ff;
  --info-dark: #0369a1;
  --error-color: #ef4444;
  --error-light: #fef2f2;
  --error-dark: #991b1b;
  
  /* Interactive Colors */
  --hover-overlay: rgba(1, 47, 98, 0.05);
  --active-overlay: rgba(1, 47, 98, 0.1);
  --focus-ring: rgba(1, 47, 98, 0.2);
  
  /* Processing & Document Colors (Theme-consistent) */
  --processing-color: var(--secondary-light);
  --processing-light: #f0f9ff;
  --processing-dark: var(--secondary-dark);
  --document-color: var(--secondary-lighter);
  --document-light: #dbeafe;
  --document-dark: var(--secondary-color);

  /* Typography */
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Base theme */
  color: var(--text-primary);
  background-color: var(--background-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: inherit;
  transition: color 0.25s ease;
}
a:hover {
  color: var(--secondary-light);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 40px; /* Space for collapsed sidepanel */
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  display: flex;
}

.content-split {
  display: flex;
  width: 100%;
  height: 100vh;
}

.chat-section {
  flex: 0 0 60%;
  min-width: 0; /* Allow flex item to shrink below content size */
}

.roadmap-section {
  flex: 0 0 40%;
  min-width: 0; /* Allow flex item to shrink below content size */
}

.content-wrapper {
  max-width: 1280px;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em var(--secondary-color)aa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em var(--secondary-color)aa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: var(--text-muted);
}

button {
  border-radius: 8px;
  border: 2px solid var(--secondary-color);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--secondary-color);
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  background-color: transparent;
  color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* Light theme variant - keeping the same colors for consistency */
@media (prefers-color-scheme: light) {
  :root {
    /* Keep the same theme colors for consistency */
    color: var(--text-primary);
    background-color: var(--background-primary);
  }

  /* Optional: Create a dark variant if needed */
  .theme-dark {
    --background-primary: #012f62;
    --background-secondary: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #012f62;
    --border-color: #ffffff;
  }
}

/* Utility classes for theme colors */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-light { background-color: var(--background-light); }
.bg-muted { background-color: var(--background-muted); }
.bg-hover { background-color: var(--background-hover); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-light { color: var(--text-light); }
.text-dark { color: var(--text-dark); }

.border-primary { border-color: var(--primary-color); }
.border-secondary { border-color: var(--secondary-color); }
.border-light { border-color: var(--border-light); }
.border-muted { border-color: var(--border-muted); }

/* Status color utilities */
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }
.text-error { color: var(--error-color); }

.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-info { background-color: var(--info-color); }
.bg-error { background-color: var(--error-color); }

.bg-success-light { background-color: var(--success-light); }
.bg-warning-light { background-color: var(--warning-light); }
.bg-info-light { background-color: var(--info-light); }
.bg-error-light { background-color: var(--error-light); }

/* Interactive state utilities */
.hover-overlay:hover { background-color: var(--hover-overlay); }
.active-overlay:active { background-color: var(--active-overlay); }
.focus-ring:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}

/* Professional shadow utilities */
.shadow-light { box-shadow: 0 1px 3px var(--shadow-light); }
.shadow-medium { box-shadow: 0 4px 6px var(--shadow-color); }
.shadow-strong { box-shadow: 0 10px 25px var(--shadow-strong); }

/* Professional Card Components */
.card {
  padding: 2em;
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 8px 25px var(--shadow-strong);
  transform: translateY(-2px);
}

.card-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-primary);
  border-radius: 12px 12px 0 0;
}

.card-body {
  padding: 1.5rem;
  background-color: var(--background-primary);
}

.card-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid var(--border-light);
  background-color: var(--background-light);
  border-radius: 0 0 12px 12px;
}

/* Professional Status Indicators */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.success {
  background-color: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success-color);
}

.status-badge.warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
  border: 1px solid var(--warning-color);
}

.status-badge.info {
  background-color: var(--info-light);
  color: var(--info-dark);
  border: 1px solid var(--info-color);
}

.status-badge.error {
  background-color: var(--error-light);
  color: var(--error-dark);
  border: 1px solid var(--error-color);
}

/* Professional Progress Indicators */
.progress-container {
  width: 100%;
  height: 8px;
  background-color: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-professional {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary-color), var(--secondary-light));
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar-professional::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Professional Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px var(--focus-ring);
}

.form-input:hover {
  border-color: var(--border-muted);
}

/* Professional Alert Components */
.alert {
  padding: 1rem 1.25rem;
  border-radius: 8px;
  border: 1px solid;
  margin-bottom: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alert.success {
  background-color: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-dark);
}

.alert.warning {
  background-color: var(--warning-light);
  border-color: var(--warning-color);
  color: var(--warning-dark);
}

.alert.info {
  background-color: var(--info-light);
  border-color: var(--info-color);
  color: var(--info-dark);
}

.alert.error {
  background-color: var(--error-light);
  border-color: var(--error-color);
  color: var(--error-dark);
}

/* Responsive adjustments for sidepanel and content split */
@media (max-width: 768px) {
  .main-content {
    margin-left: 40px; /* Smaller margin for mobile */
  }
  
  .content-split {
    flex-direction: column;
    height: auto;
  }
  
  .chat-section {
    flex: 1;
    min-height: 60vh;
  }
  
  .roadmap-section {
    flex: 1;
    min-height: 40vh;
  }
}

/* Utility class for when sidepanel is expanded */
.sidepanel-expanded .main-content {
  margin-left: 180px;
}

@media (max-width: 768px) {
  .sidepanel-expanded .main-content {
    margin-left: 200px;
  }
}
