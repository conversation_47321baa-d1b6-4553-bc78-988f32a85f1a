:root {
  /* Theme Colors */
  --primary-color: #ffffff;
  --secondary-color: #012f62;
  --secondary-light: #1a4a7a;
  --secondary-dark: #001a3d;
  --text-primary: #012f62;
  --text-secondary: #ffffff;
  --text-muted: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #012f62;
  --border-color: #e5e7eb;
  --shadow-color: rgba(1, 47, 98, 0.1);

  /* Typography */
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Base theme */
  color: var(--text-primary);
  background-color: var(--background-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: inherit;
  transition: color 0.25s ease;
}
a:hover {
  color: var(--secondary-light);
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em var(--secondary-color)aa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em var(--secondary-color)aa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: var(--text-muted);
}

button {
  border-radius: 8px;
  border: 2px solid var(--secondary-color);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--secondary-color);
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  background-color: transparent;
  color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* Light theme variant - keeping the same colors for consistency */
@media (prefers-color-scheme: light) {
  :root {
    /* Keep the same theme colors for consistency */
    color: var(--text-primary);
    background-color: var(--background-primary);
  }

  /* Optional: Create a dark variant if needed */
  .theme-dark {
    --background-primary: #012f62;
    --background-secondary: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #012f62;
    --border-color: #ffffff;
  }
}

/* Utility classes for theme colors */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.border-primary { border-color: var(--primary-color); }
.border-secondary { border-color: var(--secondary-color); }

/* Card component with theme colors */
.card {
  padding: 2em;
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 4px 6px var(--shadow-color);
}
