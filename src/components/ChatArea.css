/* Chat Area Component Styles */
.chat-area {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--background-primary);
  border-right: 1px solid var(--border-color);
}

/* Chat Header */
.chat-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--divider-color);
  background-color: var(--background-primary);
  box-shadow: 0 2px 8px var(--shadow-medium);
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-avatar {
  width: 40px;
  height: 40px;
  background-color: #ffffff;
  color: var(--secondary-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 1px 3px var(--shadow-color);
}

.title-text h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.status {
  font-size: 0.85rem;
  color: var(--success-color);
  font-weight: 500;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background-color: var(--background-primary);
}

.message {
  display: flex;
  gap: 0.75rem;
  max-width: 85%;
  animation: fadeIn 0.3s ease-in;
  margin-bottom: 1rem;
}

.message.ai {
  align-self: flex-start;
  margin-left: 1rem;
}

.message.human {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.message.ai .message-avatar {
  background-color: var(--background-primary);
  color: var(--secondary-color);
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px var(--shadow-color);
}

.message.human .message-avatar {
  background-color: var(--primary-color);
  color: var(--text-primary);
  border: 2px solid var(--secondary-color);
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.message.human .message-content {
  align-items: flex-end;
}

.message-text {
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  font-size: 0.95rem;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.file-upload-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--info-light);
  border: 1px solid var(--info-color);
  border-radius: 0.5rem;
  color: var(--info-dark);
  font-size: 0.9rem;
}

.system-output {
  background-color: var(--background-hover);
  border-left: 3px solid var(--secondary-color);
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 0.25rem;
  color: var(--text-dark);
}

.message.ai .message-text {
  background-color: var(--background-hover);
  color: var(--text-primary);
  border: 1px solid var(--divider-color);
  border-bottom-left-radius: 0.25rem;
  box-shadow: 0 1px 2px var(--shadow-light);
}

.message.human .message-text {
  background-color: var(--background-light);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-bottom-right-radius: 0.25rem;
  box-shadow: 0 1px 2px var(--shadow-light);
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  padding: 0 0.5rem;
}

/* Chat Input Area */
.chat-input-area {
  padding: 1.5rem;
  border-top: 1px solid var(--divider-color);
  background-color: var(--background-primary);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--background-primary);
  border: 2px solid var(--divider-color);
  border-radius: 1.5rem;
  padding: 0.5rem 1rem;
  transition: border-color 0.2s ease;
  box-shadow: 0 2px 4px var(--shadow-light);
}

.input-container:focus-within {
  border-color: var(--secondary-color);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.attachment-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  color: var(--text-muted);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.attachment-btn:hover {
  background-color: var(--border-color);
  color: var(--secondary-color);
}

#chat-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 0.95rem;
  color: var(--text-primary);
  padding: 0.5rem 0;
}

#chat-input::placeholder {
  color: var(--text-muted);
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  color: var(--text-muted);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover {
  background-color: var(--border-color);
  color: var(--secondary-color);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    padding: 0.75rem 1rem;
  }
  
  .chat-messages {
    padding: 0.75rem;
  }
  
  .chat-input-area {
    padding: 0.75rem 1rem;
  }
  
  .message {
    max-width: 95%;
  }
  
  .message-text {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem;
  }
}

/* Loading and Progress Indicators */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--background-hover);
  border: 1px solid var(--divider-color);
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background-color: var(--text-muted);
  border-radius: 50%;
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.processing-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, var(--processing-light) 0%, var(--document-light) 100%);
  border: 1px solid var(--processing-color);
  border-radius: 0.75rem;
  margin: 0.5rem 0;
  animation: fadeIn 0.3s ease-in;
}

.processing-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--document-light);
  border-top: 2px solid var(--processing-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text {
  color: var(--processing-dark);
  font-weight: 500;
  font-size: 0.9rem;
}

.progress-bar-container {
  width: 100%;
  height: 4px;
  background-color: var(--document-light);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--processing-color), var(--secondary-color));
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.database-operation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  border: 1px solid var(--secondary-light);
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  color: var(--secondary-dark);
  font-size: 0.9rem;
  animation: fadeIn 0.3s ease-in;
}

.database-icon {
  width: 16px;
  height: 16px;
  animation: pulse 2s infinite;
}

.ocr-processing {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  border: 1px solid var(--secondary-light);
  border-radius: 0.75rem;
  margin: 0.5rem 0;
  animation: fadeIn 0.3s ease-in;
}

.ocr-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: 0.9rem;
}

.ocr-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ocr-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--secondary-dark);
}

.ocr-step.active {
  font-weight: 500;
}

.ocr-step.completed {
  opacity: 0.7;
}

.step-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.step-icon.pending {
  background-color: #dbeafe;
}

.step-icon.active {
  background-color: var(--secondary-light);
  animation: pulse 1.5s infinite;
}

.step-icon.completed {
  background-color: var(--success-color);
}

.service-status {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--success-light);
  border: 1px solid var(--success-color);
  border-radius: 0.375rem;
  color: var(--success-dark);
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.status-dot {
  width: 6px;
  height: 6px;
  background-color: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
}