export class ChatArea {
  constructor(container) {
    this.container = container;
    this.sessionState = 'initial';
    this.sessionData = {};
    this.onProgressUpdate = null; // Callback for roadmap updates
    this.messages = [
      {
        type: 'ai',
        content: "Hi! What would you like to do today?",
        timestamp: new Date()
      }
    ];
    this.messageQueue = [];
    this.isStreaming = false;
    this.currentTypingIndicator = null;
    this.currentProcessingIndicator = null;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
    this.scrollToBottom();
  }

  render() {
    this.container.innerHTML = `
      <div class="chat-area">
        <div class="chat-header">
          <div class="chat-title">
            <div class="ai-avatar">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 8V4H8"/>
                <rect width="16" height="12" x="4" y="8" rx="2"/>
                <path d="M2 14h2"/>
                <path d="M20 14h2"/>
                <path d="M15 13v2"/>
                <path d="M9 13v2"/>
              </svg>
            </div>
            <div class="title-text">
              <h3>Service Bot</h3>
            </div>
          </div>
        </div>
        <div class="chat-messages" id="chat-messages">
          ${this.renderMessages()}
        </div>
        <div class="chat-input-area">
          <div class="input-container">
            <div class="attachment-btn" title="Attach file">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
              </svg>
            </div>
            <input type="text" id="chat-input" placeholder="Respond here..." />
            <div class="send-btn" id="send-btn" title="Send message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
              </svg>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderMessages() {
    return this.messages.map((message, index) => {
      const isSystemOutput = message.content.includes('→') || message.content.includes('■');
      const isFileUpload = message.content.includes('📎');
      
      return `
        <div class="message ${message.type}" data-message-id="${index}">
          <div class="message-avatar">
            ${message.type === 'ai' ? `
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 8V4H8"/>
                <rect width="16" height="12" x="4" y="8" rx="2"/>
                <path d="M2 14h2"/>
                <path d="M20 14h2"/>
                <path d="M15 13v2"/>
                <path d="M9 13v2"/>
              </svg>
            ` : `
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            `}
          </div>
          <div class="message-content">
            <div class="message-text ${isSystemOutput ? 'system-output' : ''} ${isFileUpload ? 'file-upload-indicator' : ''}">${message.content}</div>
            <div class="message-time">${this.formatTime(message.timestamp)}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  formatTime(timestamp) {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  attachEventListeners() {
    const input = this.container.querySelector('#chat-input');
    const sendBtn = this.container.querySelector('#send-btn');
    const attachBtn = this.container.querySelector('.attachment-btn');

    sendBtn.addEventListener('click', () => this.sendMessage());
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.sendMessage();
      }
    });

    attachBtn.addEventListener('click', () => {
      this.handleFileUpload();
    });
  }

  sendMessage() {
    const input = this.container.querySelector('#chat-input');
    const message = input.value.trim();
    
    if (message) {
      this.addMessage('human', message);
      input.value = '';
      
      // Process message based on current session state
      setTimeout(() => {
        this.processUserMessage(message);
      }, 200); // Faster initial response
    }
  }

  addMessage(type, content) {
    const message = {
      type,
      content,
      timestamp: new Date()
    };
    
    this.messages.push(message);
    this.appendNewMessage(message, this.messages.length - 1);
    this.scrollToBottom();
    
    return message;
  }

  appendNewMessage(message, index) {
    const messagesContainer = this.container.querySelector('#chat-messages');
    const isSystemOutput = message.content.includes('→') || message.content.includes('■');
    const isFileUpload = message.content.includes('📎');
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.type}`;
    messageElement.setAttribute('data-message-id', index);
    messageElement.innerHTML = `
      <div class="message-avatar">
        ${message.type === 'ai' ? `
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 8V4H8"/>
            <rect width="16" height="12" x="4" y="8" rx="2"/>
            <path d="M2 14h2"/>
            <path d="M20 14h2"/>
            <path d="M15 13v2"/>
            <path d="M9 13v2"/>
          </svg>
        ` : `
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
        `}
      </div>
      <div class="message-content">
        <div class="message-text ${isSystemOutput ? 'system-output' : ''} ${isFileUpload ? 'file-upload-indicator' : ''}">${message.content}</div>
        <div class="message-time">${this.formatTime(message.timestamp)}</div>
      </div>
    `;
    
    messagesContainer.appendChild(messageElement);
  }

  addStreamingMessage(content, delay = 0, showTyping = true) {
    this.messageQueue.push({ content, delay, showTyping });
    if (!this.isStreaming) {
      this.processMessageQueue();
    }
  }

  showTypingIndicator() {
    if (this.currentTypingIndicator) return;
    
    const messagesContainer = this.container.querySelector('#chat-messages');
    const typingElement = document.createElement('div');
    typingElement.className = 'message ai typing-message';
    typingElement.innerHTML = `
      <div class="message-avatar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 8V4H8"/>
          <rect width="16" height="12" x="4" y="8" rx="2"/>
          <path d="M2 14h2"/>
          <path d="M20 14h2"/>
          <path d="M15 13v2"/>
          <path d="M9 13v2"/>
        </svg>
      </div>
      <div class="message-content">
        <div class="typing-indicator">
          <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
          <span style="color: var(--text-muted); font-size: 0.85rem;">AI is thinking...</span>
        </div>
      </div>
    `;
    
    messagesContainer.appendChild(typingElement);
    this.currentTypingIndicator = typingElement;
    this.scrollToBottom();
  }

  hideTypingIndicator() {
    if (this.currentTypingIndicator) {
      this.currentTypingIndicator.remove();
      this.currentTypingIndicator = null;
    }
  }

  showProcessingIndicator(text, type = 'general') {
    const messagesContainer = this.container.querySelector('#chat-messages');
    const processingElement = document.createElement('div');
    processingElement.className = 'message ai processing-message';
    
    let indicatorClass = 'processing-indicator';
    let iconSvg = `<div class="processing-spinner"></div>`;
    
    if (type === 'database') {
      indicatorClass = 'database-operation';
      iconSvg = `<svg class="database-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <ellipse cx="12" cy="5" rx="9" ry="3"/>
        <path d="M3 5v14c0 3 4.5 3 9 3s9 0 9-3V5"/>
        <path d="M3 12c0 3 4.5 3 9 3s9 0 9-3"/>
      </svg>`;
    }
    
    processingElement.innerHTML = `
      <div class="message-avatar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 8V4H8"/>
          <rect width="16" height="12" x="4" y="8" rx="2"/>
          <path d="M2 14h2"/>
          <path d="M20 14h2"/>
          <path d="M15 13v2"/>
          <path d="M9 13v2"/>
        </svg>
      </div>
      <div class="message-content">
        <div class="${indicatorClass}">
          ${iconSvg}
          <span class="processing-text">${text}</span>
        </div>
      </div>
    `;
    
    messagesContainer.appendChild(processingElement);
    this.currentProcessingIndicator = processingElement;
    this.scrollToBottom();
    return processingElement;
  }

  hideProcessingIndicator() {
    if (this.currentProcessingIndicator) {
      this.currentProcessingIndicator.remove();
      this.currentProcessingIndicator = null;
    }
  }

  showOCRProcessing() {
    const messagesContainer = this.container.querySelector('#chat-messages');
    const ocrElement = document.createElement('div');
    ocrElement.className = 'message ai ocr-message';
    ocrElement.innerHTML = `
      <div class="message-avatar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 8V4H8"/>
          <rect width="16" height="12" x="4" y="8" rx="2"/>
          <path d="M2 14h2"/>
          <path d="M20 14h2"/>
          <path d="M15 13v2"/>
          <path d="M9 13v2"/>
        </svg>
      </div>
      <div class="message-content">
        <div class="ocr-processing">
          <div class="ocr-header">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            Document Processing
          </div>
          <div class="ocr-steps">
            <div class="ocr-step active" data-step="scan">
              <div class="step-icon active"></div>
              <span>Scanning document structure...</span>
            </div>
            <div class="ocr-step" data-step="extract">
              <div class="step-icon pending"></div>
              <span>Extracting text and data fields...</span>
            </div>
            <div class="ocr-step" data-step="validate">
              <div class="step-icon pending"></div>
              <span>Validating extracted information...</span>
            </div>
            <div class="ocr-step" data-step="complete">
              <div class="step-icon pending"></div>
              <span>Finalizing results...</span>
            </div>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" style="width: 0%"></div>
          </div>
        </div>
      </div>
    `;
    
    messagesContainer.appendChild(ocrElement);
    this.scrollToBottom();
    return ocrElement;
  }

  updateOCRProgress(ocrElement, step, progress) {
    const steps = ['scan', 'extract', 'validate', 'complete'];
    const currentStepIndex = steps.indexOf(step);
    
    // Update step indicators
    steps.forEach((stepName, index) => {
      const stepElement = ocrElement.querySelector(`[data-step="${stepName}"]`);
      const iconElement = stepElement.querySelector('.step-icon');
      
      if (index < currentStepIndex) {
        stepElement.classList.remove('active');
        stepElement.classList.add('completed');
        iconElement.classList.remove('active', 'pending');
        iconElement.classList.add('completed');
      } else if (index === currentStepIndex) {
        stepElement.classList.add('active');
        stepElement.classList.remove('completed');
        iconElement.classList.remove('pending', 'completed');
        iconElement.classList.add('active');
      } else {
        stepElement.classList.remove('active', 'completed');
        iconElement.classList.remove('active', 'completed');
        iconElement.classList.add('pending');
      }
    });
    
    // Update progress bar
    const progressBar = ocrElement.querySelector('.progress-bar');
    progressBar.style.width = `${progress}%`;
  }

  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      return;
    }

    const { content, delay, showTyping } = this.messageQueue.shift();
    this.isStreaming = true;
    
    if (showTyping) {
      this.showTypingIndicator();
    }
    
    setTimeout(() => {
      if (showTyping) {
        this.hideTypingIndicator();
      }
      
      const message = {
        type: 'ai',
        content: '',
        timestamp: new Date()
      };
      
      this.messages.push(message);
      const messageIndex = this.messages.length - 1;
      this.appendNewMessage(message, messageIndex);
      this.streamText(messageIndex, content);
    }, delay);
  }

  streamText(messageIndex, fullText) {
    let currentText = '';
    let index = 0;
    
    const messageElement = this.container.querySelector(`[data-message-id="${messageIndex}"] .message-text`);
    
    const streamInterval = setInterval(() => {
      if (index < fullText.length) {
        currentText += fullText[index];
        this.messages[messageIndex].content = currentText;
        if (messageElement) {
          messageElement.textContent = currentText;
        }
        this.scrollToBottom();
        index++;
      } else {
        clearInterval(streamInterval);
        // Update styling after streaming is complete
        if (messageElement) {
          const isSystemOutput = currentText.includes('→') || currentText.includes('■');
          if (isSystemOutput) {
            messageElement.classList.add('system-output');
          }
        }
        
        // Mark streaming as complete and process next message in queue
        setTimeout(() => {
          this.isStreaming = false;
          this.processMessageQueue();
        }, 200); // Faster delay before next message
      }
    }, 15); // Faster typing speed
  }

  processUserMessage(userMessage) {
    switch (this.sessionState) {
      case 'initial':
        this.handleInitialMessage(userMessage);
        break;
      case 'awaiting_employee_name':
        this.handleEmployeeName(userMessage);
        break;
      case 'awaiting_employee_selection':
        this.handleEmployeeSelection(userMessage);
        break;
      case 'awaiting_invoice':
        this.handleInvoiceMessage(userMessage);
        break;
      case 'awaiting_purchase_date':
        this.handlePurchaseDate(userMessage);
        break;
      case 'awaiting_final_response':
        this.handleFinalResponse(userMessage);
        break;
      default:
        this.addStreamingMessage("I'm not sure how to help with that. Please try again.");
    }
  }

  handleInitialMessage(message) {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('laptop') || lowerMessage.includes('assign')) {
      this.sessionState = 'awaiting_employee_name';
      this.updateRoadmapProgress(1, 'completed'); // Request Initiation completed
      this.updateRoadmapProgress(2, 'in-progress'); // Employee Identification in progress
      this.addStreamingMessage("Great. Who's the employee? (Name, ID, or email)", 1200, true);
    } else {
      this.addStreamingMessage("I can help you assign a laptop to an employee. Just say 'assign a laptop' to get started.", 800, true);
    }
  }

  handleEmployeeName(name) {
    // Show database search indicator
    const searchIndicator = this.showProcessingIndicator('Searching SuccessFactors database...', 'database');
    
    // Simulate realistic database search time
    setTimeout(() => {
      this.hideProcessingIndicator();
      
      if (name.toLowerCase().includes('steve rogers')) {
        this.sessionState = 'awaiting_employee_selection';
        this.updateRoadmapProgress(2, 'completed'); // Employee Identification completed
        this.updateRoadmapProgress(3, 'in-progress'); // Employee Selection in progress
        
        this.addStreamingMessage(`I found two matches in SuccessFactors:
 1) Steve Rogers · ID 12345 · Joined 01-Aug-2025 · Location: Bengaluru
 2) Steve Rogers · ID 98765 · Joined 12-Feb-2024 · Location: Hyderabad
 Which one? (enter 1 or 2)`, 1200, true);
      } else {
        this.addStreamingMessage(`Searching for "${name}"... No matches found. Please check the name and try again.`, 800, true);
      }
    }, 4500); // Increased database search time
  }

  handleEmployeeSelection(selection) {
    const choice = parseInt(selection.trim());
    if (choice === 1 || choice === 2) {
      // Show database update indicator
      const updateIndicator = this.showProcessingIndicator('Updating employee record...', 'database');
      
      setTimeout(() => {
        this.hideProcessingIndicator();
        
        const employees = [
          { id: 12345, name: 'Steve Rogers', doj: '2025-08-01', location: 'Bengaluru (IN)' },
          { id: 98765, name: 'Steve Rogers', doj: '2024-02-12', location: 'Hyderabad (IN)' }
        ];
        
        this.sessionData.confirmedEmployee = employees[choice - 1];
        this.sessionState = 'awaiting_invoice';
        
        this.updateRoadmapProgress(3, 'completed'); // Employee Selection completed
        this.updateRoadmapProgress(4, 'in-progress'); // Invoice Upload in progress
        
        this.addStreamingMessage(`→ confirmed_employee:
 id: ${this.sessionData.confirmedEmployee.id}
 name: ${this.sessionData.confirmedEmployee.name}
 doj: ${this.sessionData.confirmedEmployee.doj}
 location: ${this.sessionData.confirmedEmployee.location}`, 800, false);
        
        this.addStreamingMessage("Got it. Please upload the vendor invoice or spec sheet for the laptop.", 1500, true);
      }, 3200); // Increased database update time
    } else {
      this.addStreamingMessage("Please enter 1 or 2 to select the employee.", 500, true);
    }
  }

  handleInvoiceMessage(message) {
    this.addStreamingMessage("Please use the attachment button (📎) to upload the invoice file.");
  }

  handleFileUpload() {
    if (this.sessionState === 'awaiting_invoice') {
      // Create file input
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.pdf,.jpg,.jpeg,.png';
      fileInput.style.display = 'none';
      
      fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          this.processInvoiceFile(file);
        }
      });
      
      document.body.appendChild(fileInput);
      fileInput.click();
      document.body.removeChild(fileInput);
    } else {
      this.addStreamingMessage("File upload is not available at this step.");
    }
  }

  processInvoiceFile(file) {
    this.addMessage('human', `📎 Uploaded: ${file.name}`);
    
    this.updateRoadmapProgress(4, 'completed'); // Invoice Upload completed
    this.updateRoadmapProgress(5, 'in-progress'); // Document Processing in progress
    
    // Show realistic OCR processing with progress
    setTimeout(() => {
      const ocrElement = this.showOCRProcessing();
      
      // Simulate OCR steps with much longer realistic timing
      const steps = [
        { step: 'scan', progress: 25, delay: 4000 },
        { step: 'extract', progress: 60, delay: 6000 },
        { step: 'validate', progress: 85, delay: 4500 },
        { step: 'complete', progress: 100, delay: 3000 }
      ];
      
      let currentStepIndex = 0;
      
      const processNextStep = () => {
        if (currentStepIndex < steps.length) {
          const { step, progress, delay } = steps[currentStepIndex];
          this.updateOCRProgress(ocrElement, step, progress);
          
          setTimeout(() => {
            currentStepIndex++;
            if (currentStepIndex < steps.length) {
              processNextStep();
            } else {
              // OCR processing complete - keep the OCR window visible
              setTimeout(() => {
                // Don't remove the OCR element, just mark it as completed
                const ocrHeader = ocrElement.querySelector('.ocr-header');
                if (ocrHeader) {
                  ocrHeader.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                      <polyline points="14,2 14,8 20,8"/>
                      <line x1="16" y1="13" x2="8" y2="13"/>
                      <line x1="16" y1="17" x2="8" y2="17"/>
                      <polyline points="10,9 9,9 8,9"/>
                    </svg>
                    Document Processing Complete
                  `;
                }
                
                this.sessionData.ocrData = {
                  model: "Dell Latitude 5440",
                  serial_number: "DL-LAT-5440-00A1B2C3",
                  asset_tag: "LT-2025-0389",
                  cpu: "Intel i7-1365U",
                  ram: "32 GB",
                  storage: "1 TB SSD",
                  warranty_end: "2028-05-03",
                  supplier: "Ingram Micro"
                };
                
                this.updateRoadmapProgress(5, 'completed'); // Document Processing completed
                this.updateRoadmapProgress(6, 'in-progress'); // Purchase Date Entry in progress
                
                this.addStreamingMessage(`→ ocr_extract:
 model: "${this.sessionData.ocrData.model}"
 serial_number: "${this.sessionData.ocrData.serial_number}"
 asset_tag: "${this.sessionData.ocrData.asset_tag}"
 cpu: "${this.sessionData.ocrData.cpu}"
 ram: "${this.sessionData.ocrData.ram}"
 storage: "${this.sessionData.ocrData.storage}"
 warranty_end: "${this.sessionData.ocrData.warranty_end}"
 purchase_date: —
 supplier: "${this.sessionData.ocrData.supplier}"
 missing_fields: ["purchase_date"]`, 1200, false);
                
                this.sessionState = 'awaiting_purchase_date';
                this.addStreamingMessage("I couldn't find the purchase date. Could you type it in?", 2000, true);
              }, 1500);
            }
          }, delay);
        }
      };
      
      processNextStep();
    }, 1200);
  }

  handlePurchaseDate(date) {
    this.sessionData.purchaseDate = date;
    this.updateRoadmapProgress(6, 'completed'); // Purchase Date Entry completed
    this.updateRoadmapProgress(7, 'in-progress'); // Asset Record Creation in progress
    
    // Show database operations for final record creation
    const recordIndicator = this.showProcessingIndicator('Creating asset record in ServiceNow...', 'database');
    
    setTimeout(() => {
      this.hideProcessingIndicator();
      this.generateFinalAssetRecord();
    }, 3000); // Realistic database creation time
  }

  generateFinalAssetRecord() {
    const employee = this.sessionData.confirmedEmployee;
    const ocr = this.sessionData.ocrData;
    
    this.addStreamingMessage(`→ final_asset_record:
 employee_id: ${employee.id}
 asset_tag: "${ocr.asset_tag}"
 model: "${ocr.model}"
 serial_number: "${ocr.serial_number}"
 cpu: "${ocr.cpu}"
 ram: "${ocr.ram}"
 storage: "${ocr.storage}"
 purchase_date: "${this.sessionData.purchaseDate}"
 warranty_end: "${ocr.warranty_end}"
 supplier: "${ocr.supplier}"
 status: "Assigned"
 system_link: (ServiceNow URL)`, 1000, false);
    
    this.sessionState = 'completed';
    this.updateRoadmapProgress(7, 'completed'); // Asset Record Creation completed
    this.addStreamingMessage(`Laptop ${ocr.asset_tag} has been created in ServiceNow and linked to ${employee.name}

Anything else I can help with?`, 2000, true);
    this.sessionState = 'awaiting_final_response';
  }

  handleFinalResponse(message) {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('nope') && lowerMessage.includes('thanks')) {
      this.addStreamingMessage("Happy to help! Have a great day!", 1000, true);
      this.sessionState = 'final_completed';
    } else {
      this.addStreamingMessage("I'm here if you need anything else!", 800, true);
    }
  }

  updateMessages() {
    // Only used for initial render now
    const messagesContainer = this.container.querySelector('#chat-messages');
    messagesContainer.innerHTML = this.renderMessages();
  }

  scrollToBottom() {
    setTimeout(() => {
      const messagesContainer = this.container.querySelector('#chat-messages');
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
    }, 100);
  }

  // Helper method to update roadmap progress
  updateRoadmapProgress(taskId, status) {
    if (this.onProgressUpdate && typeof this.onProgressUpdate === 'function') {
      this.onProgressUpdate(taskId, status);
    }
  }
}