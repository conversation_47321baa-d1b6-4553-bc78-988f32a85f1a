/* Roadmap Panel Component Styles */
.roadmap-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--background-light);
  border-left: 1px solid var(--border-color);
}

/* Roadmap Header */
.roadmap-header {
  padding: 1.5rem;
  background-color: var(--background-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px var(--shadow-color);
}

.header-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-summary {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.estimated-time {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  background-color: var(--background-light);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

/* Roadmap Content */
.roadmap-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeline-item:hover {
  transform: translateX(2px);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.marker-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  background-color: var(--background-primary);
  z-index: 2;
  transition: all 0.2s ease;
}

.timeline-item.completed .marker-icon {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: var(--text-secondary);
}

.timeline-item.in-progress .marker-icon {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--text-secondary);
  animation: pulse 2s infinite;
}

.timeline-item.pending .marker-icon {
  background-color: var(--background-primary);
  border-color: var(--border-color);
  color: var(--text-muted);
}

.timeline-line {
  width: 2px;
  height: 40px;
  background-color: var(--border-color);
  margin-top: 0.5rem;
}

.timeline-item.completed .timeline-line {
  background-color: var(--success-color);
}

.timeline-item.in-progress .timeline-line {
  background: linear-gradient(to bottom, var(--warning-color) 50%, var(--border-color) 50%);
}

.timeline-content {
  flex: 1;
  padding-top: 0.25rem;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.task-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.task-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  flex-shrink: 0;
}

.task-status.completed {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.task-status.in-progress {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.task-status.pending {
  background-color: var(--border-light);
  color: var(--text-muted);
}

.task-description {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-muted);
  line-height: 1.4;
}

.task-meta {
  margin-top: 0.5rem;
}

.meta-text {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Roadmap Footer */
.roadmap-footer {
  padding: 1.5rem;
  background-color: var(--background-primary);
  border-top: 1px solid var(--border-color);
}

.next-action {
  background-color: var(--info-light);
  border: 1px solid #bfdbfe;
  border-radius: 0.75rem;
  padding: 1rem;
}

.next-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.next-task {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--secondary-color);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Scrollbar Styling */
.roadmap-content::-webkit-scrollbar {
  width: 6px;
}

.roadmap-content::-webkit-scrollbar-track {
  background: transparent;
}

.roadmap-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.roadmap-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .roadmap-header {
    padding: 1rem;
  }
  
  .roadmap-content {
    padding: 1rem;
  }
  
  .roadmap-footer {
    padding: 1rem;
  }
  
  .progress-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .timeline-item {
    gap: 0.75rem;
  }
  
  .marker-icon {
    width: 28px;
    height: 28px;
  }
  
  .task-title {
    font-size: 0.95rem;
  }
  
  .task-description {
    font-size: 0.85rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .timeline-item.completed .marker-icon {
    border-width: 3px;
  }
  
  .timeline-item.in-progress .marker-icon {
    border-width: 3px;
  }
  
  .timeline-item.pending .marker-icon {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .timeline-item {
    transition: none;
  }
  
  .timeline-item:hover {
    transform: none;
  }
  
  .marker-icon {
    transition: none;
  }
  
  .timeline-item.in-progress .marker-icon {
    animation: none;
  }
  
  .progress-ring-circle {
    transition: none;
  }
}