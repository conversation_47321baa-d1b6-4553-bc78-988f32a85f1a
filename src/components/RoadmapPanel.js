export class RoadmapPanel {
  constructor(container) {
    this.container = container;
    this.tasks = [];
    this.taskDefinitions = {
      1: {
        title: "Request Initiation",
        description: "User requests laptop assignment for employee"
      },
      2: {
        title: "Employee Identification",
        description: "Provide employee name, ID, or email for search"
      },
      3: {
        title: "Employee Selection",
        description: "Select correct employee from search results"
      },
      4: {
        title: "Invoice Upload",
        description: "Upload vendor invoice or laptop specification sheet"
      },
      5: {
        title: "Document Processing",
        description: "OCR extraction of laptop specifications and details"
      },
      6: {
        title: "Purchase Date Entry",
        description: "Provide missing purchase date information"
      },
      7: {
        title: "Asset Record Creation",
        description: "Generate final asset record and ServiceNow entry"
      }
    };
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
    this.updateProgress();
  }

  render() {
    this.container.innerHTML = `
      <div class="roadmap-panel">
        <div class="roadmap-header">
          <div class="header-content">
            <h3>Laptop Assignment Journey</h3>
            <div class="progress-summary">
              <div class="estimated-time">${this.getEstimatedTime()}</div>
            </div>
          </div>
        </div>
        
        <div class="roadmap-content">
          <div class="timeline">
            ${this.renderTasks()}
          </div>
        </div>
        
        <div class="roadmap-footer">
          <div class="next-action">
            <div class="next-label">Next Action:</div>
            <div class="next-task">${this.getNextTask()}</div>
          </div>
        </div>
      </div>
    `;
  }

  renderTasks() {
    return this.tasks.map((task, index) => `
      <div class="timeline-item ${task.status}" data-task-id="${task.id}">
        <div class="timeline-marker">
          <div class="marker-icon">
            ${this.getStatusIcon(task.status)}
          </div>
          ${index < this.tasks.length - 1 ? '<div class="timeline-line"></div>' : ''}
        </div>
        <div class="timeline-content">
          <div class="task-header">
            <h4 class="task-title">${task.title}</h4>
            <div class="task-status ${task.status}">
              ${this.getStatusText(task.status)}
            </div>
          </div>
          <p class="task-description">${task.description}</p>
          ${this.renderTaskMeta(task)}
        </div>
      </div>
    `).join('');
  }

  getStatusIcon(status) {
    switch (status) {
      case 'completed':
        return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 6 9 17l-5-5"/>
        </svg>`;
      case 'in-progress':
        return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>`;
      default:
        return `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"/>
        </svg>`;
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      default:
        return 'Pending';
    }
  }

  renderTaskMeta(task) {
    // Timestamps removed for cleaner appearance
    return '';
  }


  getNextTask() {
    const inProgressTask = this.tasks.find(task => task.status === 'in-progress');
    const pendingTask = this.tasks.find(task => task.status === 'pending');
    const completedTasks = this.tasks.filter(task => task.status === 'completed').length;
    const totalTasks = this.tasks.length;
    
    if (inProgressTask) {
      return inProgressTask.title;
    } else if (pendingTask) {
      return pendingTask.title;
    } else if (totalTasks === 0) {
      return 'Ready to start...';
    } else if (completedTasks === totalTasks) {
      return 'Laptop assignment solution completed successfully!';
    } else {
      return 'Continue with next step...';
    }
  }


  getEstimatedTime() {
    const completedTasks = this.tasks.filter(task => task.status === 'completed').length;
    const inProgressTasks = this.tasks.filter(task => task.status === 'in-progress').length;
    const totalTasks = this.tasks.length;
    
    if (totalTasks === 0) {
      return 'Ready to start';
    } else if (completedTasks === totalTasks) {
      return 'Solution Complete!';
    } else if (inProgressTasks > 0) {
      const remainingTasks = totalTasks - completedTasks;
      const estimatedMinutes = Math.max(1, remainingTasks * 0.5); // More realistic timing
      return `Est. ${estimatedMinutes} min remaining`;
    } else {
      return 'Ready to continue';
    }
  }

  updateProgress() {
    // Update estimated time
    const estimatedTime = this.container.querySelector('.estimated-time');
    const nextTask = this.container.querySelector('.next-task');
    
    if (estimatedTime) {
      estimatedTime.textContent = this.getEstimatedTime();
    }
    
    // Update next task message
    if (nextTask) {
      nextTask.textContent = this.getNextTask();
    }
    
    // Auto-scroll to current task
    this.scrollToCurrentTask();
  }

  attachEventListeners() {
    // Add click handlers for task items if needed
    const taskItems = this.container.querySelectorAll('.timeline-item');
    taskItems.forEach(item => {
      item.addEventListener('click', (e) => {
        const taskId = parseInt(item.dataset.taskId);
        this.onTaskClick(taskId);
      });
    });
  }

  onTaskClick(taskId) {
    // Placeholder for task click handling
    console.log(`Task ${taskId} clicked`);
  }

  // Method to update task status (can be called from outside)
  updateTaskStatus(taskId, newStatus) {
    let task = this.tasks.find(t => t.id === taskId);
    
    // If task doesn't exist, create it from definition
    if (!task && this.taskDefinitions[taskId]) {
      task = {
        id: taskId,
        title: this.taskDefinitions[taskId].title,
        description: this.taskDefinitions[taskId].description,
        status: 'pending'
      };
      this.tasks.push(task);
      // Sort tasks by id to maintain order
      this.tasks.sort((a, b) => a.id - b.id);
    }
    
    if (task) {
      task.status = newStatus;
      this.render();
      this.attachEventListeners();
      this.updateProgress();
    }
  }

  // Method to add a new task
  addTask(title, description) {
    const newTask = {
      id: this.tasks.length + 1,
      title,
      description,
      status: 'pending'
    };
    this.tasks.push(newTask);
    this.render();
    this.attachEventListeners();
    this.updateProgress();
  }

  // Method to scroll to the current active task
  scrollToCurrentTask() {
    const roadmapContent = this.container.querySelector('.roadmap-content');
    const inProgressTask = this.container.querySelector('.timeline-item.in-progress');
    
    if (roadmapContent && inProgressTask) {
      // Smooth scroll to the in-progress task
      setTimeout(() => {
        inProgressTask.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
    }
  }
}