/* SidePanel Component Styles */
.sidepanel {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background-color: var(--secondary-color);
  color: var(--text-secondary);
  transition: width 0.3s ease;
  z-index: 1000;
  box-shadow: 2px 0 10px var(--shadow-color);
  display: flex;
  flex-direction: column;
}

.sidepanel.collapsed {
  width: 55px;
}

.sidepanel.expanded {
  width: 180px;
}

/* Toggle Button */
.sidepanel-toggle {
  padding: 15px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.toggle-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.toggle-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* Sidepanel Content */
.sidepanel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
}

/* Sidepanel Bottom Section */
.sidepanel-bottom {
  padding: 15px 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.bottom-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.bottom-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.bottom-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidepanel.expanded {
    width: 100%;
    max-width: 200px;
  }

  .sidepanel.collapsed {
    width: 55px;
  }
}

/* Smooth scrollbar */
.sidepanel-content::-webkit-scrollbar {
  width: 6px;
}

.sidepanel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidepanel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidepanel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
