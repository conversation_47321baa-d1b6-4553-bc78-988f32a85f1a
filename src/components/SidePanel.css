/* SidePanel Component Styles */
.sidepanel {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background-color: var(--secondary-color);
  color: var(--text-secondary);
  transition: width 0.3s ease;
  z-index: 1000;
  box-shadow: 2px 0 10px var(--shadow-color);
  display: flex;
  flex-direction: column;
}

.sidepanel.collapsed {
  width: 60px;
}

.sidepanel.expanded {
  width: 250px;
}

/* Toggle Button */
.sidepanel-toggle {
  padding: 15px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.toggle-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.toggle-icon {
  display: block;
  line-height: 1;
}

/* Sidepanel Content */
.sidepanel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Navigation */
.sidepanel-nav {
  padding: 20px 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--primary-color);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color);
}

.nav-icon {
  font-size: 20px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 1;
  transition: opacity 0.2s ease;
}

/* Collapsed state - hide text */
.sidepanel.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidepanel.collapsed .nav-link {
  justify-content: center;
  padding: 15px 10px;
}

.sidepanel.collapsed .nav-icon {
  margin-right: 0;
}

/* Tooltip for collapsed state */
.sidepanel.collapsed .nav-link {
  position: relative;
}

.sidepanel.collapsed .nav-link::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 70px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1001;
}

.sidepanel.collapsed .nav-link:hover::after {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidepanel.expanded {
    width: 100%;
    max-width: 280px;
  }
  
  .sidepanel.collapsed {
    width: 50px;
  }
}

/* Smooth scrollbar */
.sidepanel-content::-webkit-scrollbar {
  width: 6px;
}

.sidepanel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidepanel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidepanel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
