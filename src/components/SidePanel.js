import { ChevronRight, ChevronLeft } from 'lucide';

export class SidePanel {
  constructor(container) {
    this.container = container;
    this.isExpanded = false;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    this.container.innerHTML = `
      <div class="sidepanel ${this.isExpanded ? 'expanded' : 'collapsed'}">
        <div class="sidepanel-toggle">
          <button class="toggle-btn" aria-label="Toggle sidepanel">
            <span class="toggle-icon" id="toggle-icon"></span>
          </button>
        </div>
        <div class="sidepanel-content">
          <!-- Content area for future use -->
        </div>
      </div>
    `;

    // Add Lucide icon after rendering
    this.updateToggleIcon();
  }

  updateToggleIcon() {
    const iconContainer = this.container.querySelector('#toggle-icon');
    if (iconContainer) {
      iconContainer.innerHTML = '';
      const IconComponent = this.isExpanded ? ChevronLeft : ChevronRight;
      const iconElement = IconComponent({
        size: 20,
        strokeWidth: 2
      });
      iconContainer.appendChild(iconElement);
    }
  }

  attachEventListeners() {
    const toggleBtn = this.container.querySelector('.toggle-btn');
    toggleBtn.addEventListener('click', () => this.toggle());
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
    this.render();
    this.attachEventListeners();
    this.updateMainContentMargin();
  }

  updateMainContentMargin() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      if (this.isExpanded) {
        document.body.classList.add('sidepanel-expanded');
      } else {
        document.body.classList.remove('sidepanel-expanded');
      }
    }
  }

  expand() {
    if (!this.isExpanded) {
      this.toggle();
    }
  }

  collapse() {
    if (this.isExpanded) {
      this.toggle();
    }
  }
}
