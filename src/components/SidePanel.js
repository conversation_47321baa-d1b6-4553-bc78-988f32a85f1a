// Using custom double chevron icons instead of Lucide

export class SidePanel {
  constructor(container) {
    this.container = container;
    this.isExpanded = false;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
    this.updateToggleIcon();
  }

  render() {
    this.container.innerHTML = `
      <div class="sidepanel ${this.isExpanded ? 'expanded' : 'collapsed'}">
        <div class="sidepanel-toggle">
          <button class="toggle-btn" aria-label="Toggle sidepanel">
            <span class="toggle-icon" id="toggle-icon"></span>
          </button>
        </div>
        <div class="sidepanel-content">
          <!-- Content area for future use -->
        </div>
        <div class="sidepanel-bottom">
          <div class="bottom-icon" title="Notifications">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
              <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
          </div>
          <div class="bottom-icon" title="Logout">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              <polyline points="16,17 21,12 16,7"/>
              <line x1="21" x2="9" y1="12" y2="12"/>
            </svg>
          </div>
        </div>
      </div>
    `;
  }

  updateToggleIcon() {
    const iconContainer = this.container.querySelector('#toggle-icon');
    if (iconContainer) {
      // Clear existing content
      iconContainer.innerHTML = '';

      // Create SVG element for double chevron
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', '20');
      svg.setAttribute('height', '20');
      svg.setAttribute('viewBox', '0 0 24 24');
      svg.setAttribute('fill', 'none');
      svg.setAttribute('stroke', 'currentColor');
      svg.setAttribute('stroke-width', '2');
      svg.setAttribute('stroke-linecap', 'round');
      svg.setAttribute('stroke-linejoin', 'round');

      if (this.isExpanded) {
        // Double chevron left (<<)
        const path1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path1.setAttribute('d', 'm11 17-5-5 5-5');
        svg.appendChild(path1);
        
        const path2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path2.setAttribute('d', 'm18 17-5-5 5-5');
        svg.appendChild(path2);
      } else {
        // Double chevron right (>>)
        const path1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path1.setAttribute('d', 'm9 18 6-6-6-6');
        svg.appendChild(path1);
        
        const path2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path2.setAttribute('d', 'm15 18 6-6-6-6');
        svg.appendChild(path2);
      }

      iconContainer.appendChild(svg);
    }
  }

  attachEventListeners() {
    const toggleBtn = this.container.querySelector('.toggle-btn');
    toggleBtn.addEventListener('click', () => this.toggle());
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
    this.render();
    this.attachEventListeners();
    this.updateToggleIcon();
    this.updateMainContentMargin();
  }

  updateMainContentMargin() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      if (this.isExpanded) {
        document.body.classList.add('sidepanel-expanded');
      } else {
        document.body.classList.remove('sidepanel-expanded');
      }
    }
  }

  expand() {
    if (!this.isExpanded) {
      this.toggle();
    }
  }

  collapse() {
    if (this.isExpanded) {
      this.toggle();
    }
  }
}
