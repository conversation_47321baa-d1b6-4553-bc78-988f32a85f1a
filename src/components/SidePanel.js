export class SidePanel {
  constructor(container) {
    this.container = container;
    this.isExpanded = false;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    this.container.innerHTML = `
      <div class="sidepanel ${this.isExpanded ? 'expanded' : 'collapsed'}">
        <div class="sidepanel-toggle">
          <button class="toggle-btn" aria-label="Toggle sidepanel">
            <span class="toggle-icon">${this.isExpanded ? '<<' : '>>'}</span>
          </button>
        </div>
        <div class="sidepanel-content">
          <nav class="sidepanel-nav">
            <ul class="nav-list">
              <li class="nav-item">
                <a href="#dashboard" class="nav-link" data-tooltip="Dashboard">
                  <span class="nav-icon">📊</span>
                  <span class="nav-text">Dashboard</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="#services" class="nav-link" data-tooltip="Services">
                  <span class="nav-icon">🛠️</span>
                  <span class="nav-text">Services</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="#analytics" class="nav-link" data-tooltip="Analytics">
                  <span class="nav-icon">📈</span>
                  <span class="nav-text">Analytics</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="#settings" class="nav-link" data-tooltip="Settings">
                  <span class="nav-icon">⚙️</span>
                  <span class="nav-text">Settings</span>
                </a>
              </li>
              <li class="nav-item">
                <a href="#help" class="nav-link" data-tooltip="Help">
                  <span class="nav-icon">❓</span>
                  <span class="nav-text">Help</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    const toggleBtn = this.container.querySelector('.toggle-btn');
    toggleBtn.addEventListener('click', () => this.toggle());
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
    this.render();
    this.attachEventListeners();
    this.updateMainContentMargin();
  }

  updateMainContentMargin() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      if (this.isExpanded) {
        document.body.classList.add('sidepanel-expanded');
      } else {
        document.body.classList.remove('sidepanel-expanded');
      }
    }
  }

  expand() {
    if (!this.isExpanded) {
      this.toggle();
    }
  }

  collapse() {
    if (this.isExpanded) {
      this.toggle();
    }
  }
}
