export class SidePanel {
  constructor(container) {
    this.container = container;
    this.isExpanded = false;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    this.container.innerHTML = `
      <div class="sidepanel ${this.isExpanded ? 'expanded' : 'collapsed'}">
        <div class="sidepanel-toggle">
          <button class="toggle-btn" aria-label="Toggle sidepanel">
            <span class="toggle-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                ${this.isExpanded
                  ? '<path d="m15 18-6-6 6-6"/>' // ChevronLeft
                  : '<path d="m9 18 6-6-6-6"/>'  // ChevronRight
                }
              </svg>
            </span>
          </button>
        </div>
        <div class="sidepanel-content">
          <!-- Content area for future use -->
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    const toggleBtn = this.container.querySelector('.toggle-btn');
    toggleBtn.addEventListener('click', () => this.toggle());
  }

  toggle() {
    this.isExpanded = !this.isExpanded;
    this.render();
    this.attachEventListeners();
    this.updateMainContentMargin();
  }

  updateMainContentMargin() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      if (this.isExpanded) {
        document.body.classList.add('sidepanel-expanded');
      } else {
        document.body.classList.remove('sidepanel-expanded');
      }
    }
  }

  expand() {
    if (!this.isExpanded) {
      this.toggle();
    }
  }

  collapse() {
    if (this.isExpanded) {
      this.toggle();
    }
  }
}
