import './style.css'
import './components/SidePanel.css'
import './components/ChatArea.css'
import './components/RoadmapPanel.css'
import { SidePanel } from './components/SidePanel.js'
import { ChatArea } from './components/ChatArea.js'
import { RoadmapPanel } from './components/RoadmapPanel.js'

document.querySelector('#app').innerHTML = `
  <div id="sidepanel-container"></div>
  <div class="main-content">
    <div class="content-split">
      <div class="chat-section" id="chat-container"></div>
      <div class="roadmap-section" id="roadmap-container"></div>
    </div>
  </div>
`

// Initialize components
// Initialize SidePanel
const sidePanelContainer = document.querySelector('#sidepanel-container')
const sidePanel = new SidePanel(sidePanelContainer)

// Initialize ChatArea (60% width)
const chatContainer = document.querySelector('#chat-container')
const chatArea = new ChatArea(chatContainer)

// Initialize RoadmapPanel (40% width)
const roadmapContainer = document.querySelector('#roadmap-container')
const roadmapPanel = new RoadmapPanel(roadmapContainer)

// Connect chat and roadmap for interactive updates
// Example: When chat progresses, update roadmap status
chatArea.onProgressUpdate = (taskId, status) => {
  roadmapPanel.updateTaskStatus(taskId, status)
}
